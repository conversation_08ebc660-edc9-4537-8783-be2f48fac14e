%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 61
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12386, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Keys:
  - __PanelContainer__rootVisualContainer__bp-window-split-view__UnityEngine.UIElements.TwoPaneSplitView
  - __PanelContainer__rootVisualContainer__bp-window-split-view__bp-scroll-view-platform-list__bp-platform-profile-list__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__bp-window-split-view__bp-scroll-view-platform-list__bp-platform-profile-list__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+<PERSON>rollerSlider
  - __PanelContainer__rootVisualContainer__bp-window-split-view__bp-scroll-view-platform-list__bp-custom-profile-list__UnityEngine.UIElements.ListView
  - __PanelContainer__rootVisualContainer__bp-window-split-view__bp-scroll-view-platform-list__bp-custom-profile-list__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__bp-window-split-view__bp-scroll-view-platform-list__VerticalScroller__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__bp-window-split-view__bp-scroll-view-platform-list__HorizontalScroller__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__bp-window-split-view__bp-build-profile-editor-inspector__bp-scene-list-foldout__UnityEngine.UIElements.Foldout
  - __PanelContainer__rootVisualContainer__bp-window-split-view__bp-build-profile-editor-inspector__bp-scripting-defines-foldout__UnityEngine.UIElements.Foldout
  - __PanelContainer__rootVisualContainer__bp-window-split-view__bp-build-profile-editor-inspector__bp-build-settings-foldout__UnityEngine.UIElements.Foldout
  - __PanelContainer__rootVisualContainer__bp-window-split-view__bp-build-profile-editor-inspector__bp-player-settings-foldout__UnityEngine.UIElements.Foldout
  - __PanelContainer__rootVisualContainer__bp-window-split-view__bp-build-profile-editor-inspector__VerticalScroller__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__bp-window-split-view__bp-build-profile-editor-inspector__HorizontalScroller__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  m_Values:
  - '{"m_FixedPaneDimension":225.0}'
  - '{"m_ShowAlternatingRowBackgrounds":0,"serializedVirtualizationData":{"scrollOffset":{"x":0.0,"y":0.0},"firstVisibleIndex":0,"contentPadding":0.0,"contentHeight":0.0,"anchoredItemIndex":0,"anchorOffset":0.0},"m_SelectedIds":[2]}'
  - '{"m_Value":0.0,"m_LowValue":0.0,"m_HighValue":0.0}'
  - '{"m_ShowAlternatingRowBackgrounds":0,"serializedVirtualizationData":{"scrollOffset":{"x":0.0,"y":0.0},"firstVisibleIndex":0,"contentPadding":0.0,"contentHeight":0.0,"anchoredItemIndex":0,"anchorOffset":0.0},"m_SelectedIds":[]}'
  - '{"m_Value":0.0,"m_LowValue":0.0,"m_HighValue":NaN}'
  - '{"m_Value":0.0,"m_LowValue":0.0,"m_HighValue":-491.0}'
  - '{"m_Value":0.0,"m_LowValue":0.0,"m_HighValue":0.0}'
  - '{"m_Value":true}'
  - '{"m_Value":true}'
  - '{"m_Value":true}'
  - '{"m_Value":true}'
  - '{"m_Value":0.0,"m_LowValue":0.0,"m_HighValue":-315.0}'
  - '{"m_Value":0.0,"m_LowValue":0.0,"m_HighValue":0.0}'
